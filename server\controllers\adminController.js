import Profession from "../models/Profession.js";
import SubCategory from "../models/SubCategory.js";


// POST /api/admin/professions
export const addProfession = async (req, res) => {
  const { title, subcategories, icon } = req.body;

  try {
    // Validate required fields
    if (!title) {
      return res.status(400).json({ message: "Profession title is required" });
    }

    if (!subcategories || !Array.isArray(subcategories) || subcategories.length === 0) {
      return res.status(400).json({ message: "At least one subCategory group is required" });
    }

    // Validate subCategory structure
    for (const subcategoryGroup of subcategories) {
      if (!subcategoryGroup.title || !subcategoryGroup.items || !Array.isArray(subcategoryGroup.items) || subcategoryGroup.items.length === 0) {
        return res.status(400).json({
          message: "Each subCategory group must have a title and at least one item"
        });
      }
    }

    // Check if profession already exists
    const exists = await Profession.findOne({ name: title });
    if (exists) {
      return res.status(400).json({ message: "Profession already exists" });
    }

    // Create the profession with the admin user ID
    const profession = new Profession({
      name: title,
      icon,
      createdBy: req.user.userId
    });
    await profession.save();

    // Create subcategories grouped by titles
    const subcategoryPromises = [];

    subcategories.forEach(subcategoryGroup => {
      subcategoryGroup.items.forEach(subcategoryName => {
        const subCategory = new SubCategory({
          name: subcategoryName,
          title: subcategoryGroup.title,
          profession: profession._id,
          createdBy: req.user.userId
        });
        subcategoryPromises.push(subCategory.save());
      });
    });

    const createdSubcategories = await Promise.all(subcategoryPromises);

    res.status(201).json({
      success: true,
      profession,
      subcategories: createdSubcategories
    });
  } catch (err) {
    res.status(500).json({ success: false, message: err.message });
  }
};

// POST /api/admin/subcategories
export const addSubcategory = async (req, res) => {
  const { professionId, subCatTitle, subCategory } = req.body;

  try {
    // Validate required fields
    if (!professionId) {
      return res.status(400).json({ message: "Profession ID is required" });
    }
    if (!subCatTitle) {
      return res.status(400).json({ message: "Subcategory title is required" });
    }
    if (!subCategory || !Array.isArray(subCategory) || subCategory.length === 0) {
      return res.status(400).json({ message: "At least one subCategory is required" });
    }

    // Check if profession exists
    const profession = await Profession.findById(professionId);
    if (!profession) {
      return res.status(404).json({ message: "Profession not found" });
    }

    // Create multiple subcategories
    const subcategoryPromises = subCategory.map(subcategoryName => {
      const newSubcategory = new SubCategory({
        name: subcategoryName,
        title: subCatTitle,
        profession: professionId,
        createdBy: req.user.userId
      });
      return newSubcategory.save();
    });

    const createdSubcategories = await Promise.all(subcategoryPromises);

    res.status(201).json({
      success: true,
      subcategories: createdSubcategories,
      message: `${createdSubcategories.length} subcategories added successfully`
    });
  } catch (err) {
    res.status(500).json({ success: false, message: err.message });
  }
};

export const getAllProfessions = async (req, res) => {
  try {
    const professions = await Profession.find();
    res.status(200).json({ success: true, professions });
  } catch (err) {
    res.status(500).json({ success: false, message: err.message });
  }
};

export const getSubcategoriesByProfession = async (req, res) => {
  const { professionId } = req.params;

  try {
    const subcategories = await SubCategory.find({ profession: professionId });

    res.status(200).json({ success: true, subcategories });
  } catch (err) {
    res.status(500).json({ success: false, message: err.message });
  }
};

